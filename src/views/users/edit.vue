<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUsers } from '@/composables/useUsers'
import { useAuth } from '@/composables/useAuth'
import { useCloudinary } from '@/composables/useCloudinary'
import { User } from '@/models/User'
import type { UserRole } from '@/types/user'
import { Timestamp } from 'firebase/firestore'
import { X } from 'lucide-vue-next'

interface NameComparison {
  auth: {
    displayName: string
    firstName: string
    lastName: string
  }
  firestore: {
    firstName: string
    lastName: string
  }
  matches: {
    firstName: boolean
    lastName: boolean
  }
}

const props = defineProps<{
  id: string
}>()

const router = useRouter()
const { getUser, updateUserProfile, isCurrentUser } = useUsers()
const { user: authUser, isAdmin } = useAuth()
const { uploadImage, uploadError } = useCloudinary()

const user = ref<User | null>(null)
const nameComparison = ref<NameComparison | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const selectedNameSource = ref<'auth' | 'firestore' | null>(null)
const selectedRole = ref<string>('')

// Form data
const formData = ref({
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  phone: '',
  address: '',
  birthday: '' as string,
  tags: [] as string[],
  roles: [] as UserRole[],
  photoFile: null as File | null,
  photoURL: null as string | null,
  prefs: {
    isSubscribed: false,
    viewAsGrid: false
  },
  isDisabled: false
})

const newTag = ref('')

// Role options
const roleOptions = [
  { label: 'User', value: 'user' },
  { label: 'Artist', value: 'artist' },
  { label: 'Band Leader', value: 'bandLeader' },
  { label: 'Administrator', value: 'admin' }
]

const filteredRoleOptions = computed(() => {
  const currentRoles = formData.value.roles.map(role => role.type)
  return roleOptions.filter(option => !currentRoles.includes(option.value as UserRole['type']))
})

const canEditNames = computed(() => {
  if (!user.value) return false
  return isCurrentUser(user.value) || isAdmin.value
})

const canEditRoles = computed(() => isAdmin.value)

const selectedNames = computed(() => ({
  firstName: formData.value.firstName,
  lastName: formData.value.lastName
}))

// Watch for changes in the selected name source
watch(selectedNameSource, (newSource) => {
  if (!nameComparison.value) return

  if (newSource === 'auth') {
    formData.value.firstName = nameComparison.value.auth.firstName
    formData.value.lastName = nameComparison.value.auth.lastName
  } else if (newSource === 'firestore') {
    formData.value.firstName = nameComparison.value.firestore.firstName
    formData.value.lastName = nameComparison.value.firestore.lastName
  }
})

onMounted(async () => {
  try {
    const userData = await getUser(props.id)
    if (!userData) {
      throw new Error('User not found')
    }
    user.value = new User(userData)

    // Initialize form data with user values
    formData.value = {
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      username: userData.username || '',
      email: userData.email || '',
      phone: userData.phone || '',
      address: userData.address || '',
      birthday: userData.birthday
        ? userData.birthday.toDate().toLocaleDateString('en-GB')
        : '',
      tags: [...(userData.tags || [])],
      roles: [...(userData.roles || [])],
      photoFile: null,
      photoURL: userData.photoURL || null,
      prefs: {
        isSubscribed: userData.prefs?.isSubscribed || false,
        viewAsGrid: userData.prefs?.viewAsGrid || false
      },
      isDisabled: userData.isDisabled || false
    }

    // If it's current user, check for name mismatch
    if (authUser.value?.uid === props.id && authUser.value.displayName) {
      const nameParts = authUser.value.displayName.split(' ')
      const authFirstName = nameParts[0] || ''
      const authLastName = nameParts.slice(1).join(' ') || ''
      nameComparison.value = {
        auth: {
          displayName: authUser.value.displayName,
          firstName: authFirstName,
          lastName: authLastName
        },
        firestore: {
          firstName: userData.firstName,
          lastName: userData.lastName
        },
        matches: {
          firstName: authFirstName === userData.firstName,
          lastName: authLastName === userData.lastName
        }
      }
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
})

async function saveUser() {
  if (!user.value) return
  try {
    isLoading.value = true
    error.value = null

    let photoURL = formData.value.photoURL

    // Upload photo if a new file was selected
    if (formData.value.photoFile) {
      try {
        photoURL = await uploadImage(formData.value.photoFile, 'user-photos')
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Failed to upload photo')
      }
    }

    // Convert birthday back to Timestamp if it exists
    // Parse as UTC noon to avoid timezone issues
    const birthday = formData.value.birthday
      ? Timestamp.fromDate(new Date(formData.value.birthday + 'T12:00:00Z'))
      : null

    // Update user profile with all fields
    const success = await updateUserProfile({
      id: props.id,
      firstName: formData.value.firstName,
      lastName: formData.value.lastName,
      username: formData.value.username,
      phone: formData.value.phone,
      address: formData.value.address,
      birthday,
      tags: formData.value.tags,
      roles: formData.value.roles,
      photoURL,
      prefs: formData.value.prefs,
      isDisabled: formData.value.isDisabled
    })

    if (!success) throw new Error('Failed to update user profile')

    router.push('/users')
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    isLoading.value = false
  }
}

function addTag() {
  if (!newTag.value.trim()) return
  if (!formData.value.tags.includes(newTag.value.trim())) {
    formData.value.tags.push(newTag.value.trim())
  }
  newTag.value = ''
}

function removeTag(index: number) {
  formData.value.tags.splice(index, 1)
}

function handleRoleSelect(roleType: string) {
  // Ignore empty value (placeholder selection)
  if (!roleType) return

  // Add the selected role
  formData.value.roles.push({ type: roleType as UserRole['type'] })

  // Reset the select
  selectedRole.value = ''
}

function removeRole(index: number) {
  formData.value.roles.splice(index, 1)
}

function cancelEdit() {
  router.push('/users')
}
</script>

<template>
  <BaseSection title="Edit User">
    <template v-if="isLoading">
      <BaseCard>Loading user details...</BaseCard>
    </template>

    <template v-else-if="error">
      <BaseCard>{{ error }}</BaseCard>
    </template>

    <template v-else-if="user">
      <!-- Name Mismatch Warning -->
      <div v-if="nameComparison && (!nameComparison.matches.firstName || !nameComparison.matches.lastName)"
        class="name-mismatch">
        <div class="name-mismatch-header">
          <h3>Name Mismatch Detected</h3>
        </div>

        <div v-if="canEditNames" class="name-options">
          <fieldset>
            <div class="name-options-grid">
              <h4>Which name is correct?</h4>
              <BaseButton purpose="secondary" :class="{ 'name-option-button-selected': selectedNameSource === 'auth' }"
                @click="selectedNameSource = 'auth'">
                Use {{ nameComparison.auth.firstName }} {{ nameComparison.auth.lastName }}
              </BaseButton>
              <BaseButton purpose="secondary"
                :class="{ 'name-option-button-selected': selectedNameSource === 'firestore' }"
                @click="selectedNameSource = 'firestore'">
                Use {{ nameComparison.firestore.firstName }} {{ nameComparison.firestore.lastName }}
              </BaseButton>
            </div>
          </fieldset>

          <div v-if="selectedNameSource" class="name-preview">
            <div class="name-preview-header">
              <h4>Names that will be saved:</h4>
            </div>
            <div class="name-preview-details">
              <div class="name-field">
                <span class="name-label">First Name:</span>
                <strong>{{ selectedNames.firstName }}</strong>
              </div>
              <div class="name-field">
                <span class="name-label">Last Name:</span>
                <strong>{{ selectedNames.lastName }}</strong>
              </div>
            </div>
            <p class="name-preview-note">
              These names will be synchronized in both the authentication system and database.
            </p>
          </div>
        </div>

        <div v-else class="name-mismatch-readonly">
          <p>Only administrators or the account owner can resolve name mismatches.</p>
        </div>
      </div>

      <form @submit.prevent="saveUser" class="edit-form">
        <!-- Basic Information -->
        <div class="form-section">
          <h3>Basic Information</h3>

          <div class="form-row">
            <BaseInput id="firstName" v-model="formData.firstName" label="First Name" type="text" required
              :disabled="!canEditNames" />

            <BaseInput id="lastName" v-model="formData.lastName" label="Last Name" type="text" required
              :disabled="!canEditNames" />
          </div>

          <BaseInput id="username" v-model="formData.username" label="Username" type="text" required />

          <BaseInput id="email" v-model="formData.email" label="Email" type="email" disabled />
        </div>

        <!-- Photo Upload -->
        <div class="form-section small">
          <h3>Profile Photo</h3>
          <BasePhotoUpload v-model="formData.photoFile" :preview-url="formData.photoURL" :aspect-ratio="1"
            @error="error = $event" />
          <p v-if="uploadError" class="error-message">{{ uploadError }}</p>
        </div>

        <!-- Contact Information -->
        <div class="form-section">
          <h3>Contact Information</h3>

          <BaseInput id="phone" v-model="formData.phone" label="Phone" type="tel" />

          <BaseInput id="address" v-model="formData.address" label="Address" type="text" />

          <BaseInput id="birthday" v-model="formData.birthday" label="Birthday" type="date" />
        </div>

        <!-- Tags -->
        <div class="form-section small">
          <h3>Tags</h3>
          <div class="tags-container">
            <div class="tags-list">
              <BaseBadge v-for="(tag, index) in formData.tags" :key="index" variant="outline" purpose="secondary"
                closeable @close="removeTag(index)">
                {{ tag }}
              </BaseBadge>
            </div>
            <div class="tag-input-container">
              <BaseInput id="newTag" v-model="newTag" label="Add Tag" type="text" placeholder="Enter a tag"
                @keyup.enter="addTag" />
              <BaseButton type="button" @click="addTag" size="compact" title="Add Tag">Add</BaseButton>
            </div>
          </div>
        </div>

        <!-- Roles (Admin only) -->
        <div v-if="canEditRoles" class="form-section small">
          <h3>Roles</h3>
          <div class="roles-container">
            <BaseBadge v-for="(role, index) in formData.roles" :key="role.type" purpose="secondary" closeable
              @close="removeRole(index)">
              {{ role.type }}
            </BaseBadge>
          </div>
          <BaseSelect v-if="filteredRoleOptions.length" id="role-select" v-model="selectedRole"
            aria-placeholder="Select Role to Add" placeholder="Select Role to Add" :options="filteredRoleOptions"
            @update:modelValue="handleRoleSelect" />
        </div>

        <!-- Preferences -->
        <div class="form-section small">
          <h3>Preferences</h3>
          <div class="preferences-container">
            <BaseToggle id="isSubscribed" v-model="formData.prefs.isSubscribed" label="Email Subscriptions" />
            <BaseToggle id="viewAsGrid" v-model="formData.prefs.viewAsGrid" label="Grid View" />
          </div>
        </div>

        <!-- Admin Settings (Admin only) -->
        <div v-if="canEditRoles" class="form-section">
          <h3>Account Settings</h3>
          <BaseToggle id="isDisabled" v-model="formData.isDisabled" label="Account Disabled" purpose="danger" />
        </div>

        <div class="form-actions align-end">
          <BaseButton type="submit" :disabled="isLoading">
            {{ isLoading ? 'Saving...' : 'Save Changes' }}
          </BaseButton>
          <BaseButton type="button" @click="cancelEdit" variant="outline">Cancel</BaseButton>
        </div>
      </form>
    </template>
  </BaseSection>
</template>

<style scoped>
.edit-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: var(--space-m);
  width: 100%;
  padding: 0;
  border: none;
  background: var(--color-bg-1);
}

.form-section {
  display: flex;
  flex-grow: 1;
  max-width: 100%;
  flex-direction: column;
  gap: var(--space-m);
  padding: var(--space-l);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  background: var(--color-surface);

  &.small {
    max-width: 400px;
  }
}

.form-section h3 {
  margin: 0 0 var(--space-m) 0;
  font-size: var(--step-1);
  font-weight: 600;
  color: var(--color-text);
}

.align-end {
  align-self: flex-end;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-m);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-actions {
  display: flex;
  gap: var(--space-s);
  margin-top: var(--space-m);
  padding-top: var(--space-m);
  border-top: 1px solid var(--color-border);
}

/* Tags */
.tags-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.tag-item {
  position: relative;
  padding-right: var(--space-l);
}

.tag-remove {
  position: absolute;
  top: 50%;
  right: var(--space-xs);
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-muted);
  cursor: pointer;
  font-size: var(--step-1);
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-remove:hover {
  color: var(--color-danger);
}

.tag-input-container {
  display: flex;
  gap: var(--space-s);
  align-items: end;
}

/* Roles */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-m);
}

/* Preferences */
.preferences-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
}

/* Error message */
.error-message {
  color: var(--color-danger);
  font-size: var(--step--1);
  margin: var(--space-xs) 0 0 0;
}

.name-mismatch {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  margin-bottom: var(--space-xl);
}

.name-mismatch-header {
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.name-mismatch-header h3 {
  color: var(--color-warning);
  font-size: var(--step-1);
  font-weight: 600;
  margin: 0;
}

.name-mismatch-explanation {
  color: var(--color-text-muted);
  font-size: var(--step-0);
  line-height: 1.5;
  margin: 0;
}

.name-options {
  display: grid;
  gap: var(--space-s);
}

.name-options fieldset {
  border: 1px solid var(--color-warning);
  background-color: var(--color-brand-dark);
  border-radius: var(--radius-l);
  padding: var(--space-s);
}

.name-options legend {
  padding: 0 var(--space-s);
  font-weight: 500;
  color: var(--color-warning);
  font-size: var(--step-0);
}

.name-options-grid {
  display: grid;
  gap: var(--space-s);
  grid-template-columns: auto 1fr 1fr;
}

.name-option {
  display: flex;
  gap: var(--space-s);
  padding: var(--space-m);
  border: 2px solid var(--color-border-subtle);
  border-radius: var(--radius-l);
  background: var(--color-background);
  transition: all 0.2s ease-in-out;
}

.name-option:hover {
  border-color: var(--color-border-primary);
  background: var(--color-bg-subtle);
}

.name-option-radio {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding-top: var(--space-xs);
}

.name-option-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.radio-circle {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border-muted);
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.name-option-radio input[type="radio"]:checked+.radio-circle {
  border-color: var(--color-border-success);
  background: var(--color-bg-success);
  box-shadow: inset 0 0 0 4px var(--color-bg-default);
}

.name-option label {
  flex: 1;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: var(--space-s);
}

.name-option-header h4 {
  margin: 0;
  font-size: var(--step-0);
  font-weight: 500;
  color: var(--color-text-default);
}

.name-option-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.name-field {
  display: flex;
  gap: var(--space-xs);
  align-items: baseline;
  font-size: var(--step--1);
}

.name-label {
  color: var(--color-text-muted);
  min-width: 80px;
}

.name-preview {
  background: var(--color-success-dark);
  border: 1px solid var(--color-success);
  border-radius: var(--radius-l);
  padding: var(--space-l);
}

.name-preview-header h4 {
  margin: 0 0 var(--space-m);
  font-size: var(--step-0);
  font-weight: 500;
}

.name-preview-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  margin-bottom: var(--space-m);
}

.name-preview-note {
  font-size: var(--step--1);
  margin: 0;
}

.name-mismatch-readonly {
  color: var(--color-text-muted);
  font-style: italic;
  padding: var(--space-m);
  background: var(--color-surface);
  border-radius: var(--radius-m);
}

.name-option-button-selected {
  border-color: var(--color-success);
  background: var(--color-success-dark);
  color: var(--color-text);
}
</style>
