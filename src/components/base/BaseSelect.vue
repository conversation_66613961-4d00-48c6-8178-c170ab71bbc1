<script setup lang="ts">
import { computed, watch } from 'vue'
import { X } from 'lucide-vue-next'

type Option = {
  label?: string
  value?: any
}

type Props = {
  id: string
  modelValue?: any
  options?: Option[]
  label?: string
  error?: string
  required?: boolean
  clearable?: boolean
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  clearable: true
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const handleClear = (event: Event) => {
  event.stopPropagation()
  emit('update:modelValue', null)
}

const displayOptions = computed(() => {
  if (!props.options || !props.options.length) return [{ label: 'No options available', value: '' }]

  const originalOptions = [...props.options]

  if (!props.label) {
    originalOptions.unshift({ label: props.placeholder || 'Select an option...', value: '' })
  }

  return originalOptions
})
</script>

<template>
  <div class="base-select" :class="{ 'disabled': options?.length === 0 }">
    <label v-if="label" :for="id">{{ label }} <span v-if="required" class="required-indicator">*</span></label>

    <div class="select-wrapper">
      <select :id :value="modelValue" @input="$emit('update:modelValue', ($event.target as HTMLSelectElement).value)"
        :required>
        <option v-for="option in displayOptions || []" :key="option.value || option.label" :value="option.value">
          {{ option.label || option.value }}
        </option>
      </select>
      <button v-if="clearable && modelValue" type="button" class="clear-button" @click="handleClear" tabindex="-1">
        <X class="icon" />
      </button>
      <span class="required-indicator no-label" v-if="!label && required">*</span>
    </div>
    <span v-if="error" class="error">{{ error }}</span>
  </div>
</template>

<style scoped>
.base-select {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  label {
    font-size: var(--step--1);
    font-weight: 500;
  }
}

.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.select-wrapper select {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  appearance: none;
  background-color: var(--bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  color: var(--color-text);
  font-size: inherit;
  font-family: inherit;
  line-height: 1;
  padding: 0.33em 2.5em 0.33em .66em;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button {
  position: absolute;
  /* Position before the dropdown arrow */
  background: none;
  border: none;
  color: var(--color-text-soft);
  cursor: pointer;
  right: .1em;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  border-radius: 50%;
  transition: all 0.2s ease;

  .icon {
    font-size: .75em;
  }
}

.clear-button:hover {
  color: var(--color-text);
  background-color: var(--color-border);
}

.clear-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.select-wrapper select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.select-wrapper select:disabled {
  background-color: var(--color-background-alt);
  cursor: not-allowed;
  opacity: 0.7;
}

.required-indicator {
  color: var(--color-danger);

  &.no-label {
    position: absolute;
    right: -1em;
  }
}

.error {
  color: var(--color-danger);
  font-size: 0.9rem;
}

select[value=""] {
  color: var(--color-text-muted);
}
</style>
