<script setup lang="ts">
import { computed } from 'vue'
import { useToggleStyles, type StyleVariant, type StylePurpose } from '@/composables/useSystemStyles'

export type ToggleSize = 'tiny' | 'compact' | 'default' | 'large'

type Props = {
  modelValue: boolean
  label?: string
  size?: ToggleSize
  variant?: StyleVariant
  purpose?: StylePurpose
  disabled?: boolean
  required?: boolean
  name?: string
  id?: string
}

type Emits = {
  (e: 'update:modelValue', value: boolean): void
  (e: 'change', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  variant: 'solid',
  purpose: 'primary',
  disabled: false,
  required: false
})

const emit = defineEmits<Emits>()

const toggleClasses = computed(() => [
  `toggle--${props.size}`,
  `toggle--${props.variant}`,
  `toggle--${props.purpose}`,
  {
    'toggle--checked': props.modelValue,
    'toggle--disabled': props.disabled
  }
])

function handleChange(event: Event) {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.checked)
  emit('change', target.checked)
}

const { currentStyles } = useToggleStyles(props.variant, props.purpose, props.modelValue)
</script>

<template>
  <label :class="['toggle-wrapper', `toggle-wrapper--${size}`]" :title="label || name">
    <input type="checkbox" :checked="modelValue" :disabled :required :name :id @change="handleChange"
      class="toggle-input">
    <span :class="['toggle-track', toggleClasses]">
      <span class="toggle-thumb"></span>
    </span>
    <span v-if="label" class="toggle-label">{{ label }}</span>
    <slot></slot>
  </label>
</template>

<style scoped>
.toggle-wrapper {
  display: inline-flex;
  font-size: 1em;
  align-items: center;
  gap: .33em;
  cursor: pointer;
  overflow: hidden;
}

.toggle-wrapper--tiny {
  font-size: var(--step--2);
}

.toggle-wrapper--compact {
  font-size: var(--step--1);
}

.toggle-wrapper--large {
  font-size: var(--step-1);
}

.toggle-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-track {
  position: relative;
  display: inline-block;
  width: 2.2em;
  height: 1.2em;
  border-radius: 100vw;
  transition: all 0.2s ease;
  background-color: v-bind('modelValue ? (currentStyles.checked?.background || currentStyles.background) : currentStyles.background');
  background-image: linear-gradient(to bottom right, #fff2, #0002);
  border: 1px solid v-bind('modelValue ? (currentStyles.checked?.border || currentStyles.border) : currentStyles.border');
  box-shadow: .06em .06em .09em rgba(0, 0, 0, .25) inset, -.06em -.06em .09em rgba(255, 255, 255, .5) inset;
}

.toggle-thumb {
  position: absolute;
  top: 0.1em;
  left: 0.1em;
  width: 0.9em;
  height: 0.9em;
  border-radius: 50%;
  background-color: v-bind('modelValue ? currentStyles.checked?.thumb : currentStyles.thumb');
  background-image: linear-gradient(to bottom right, #fff2, #0002);
  box-shadow: .06em .06em .09em rgba(0, 0, 0, .25), .06em .06em .09em rgba(255, 255, 255, .5) inset;
  transition: all 0.2s ease;
}

/* Size Variants */
.toggle--tiny {
  width: 1.8em;
  height: 0.9em;

  .toggle-thumb {
    width: 0.6em;
    height: 0.6em;
  }
}

.toggle--compact {
  width: 2em;
  height: 1em;

  .toggle-thumb {
    width: 0.7em;
    height: 0.7em;
  }
}

.toggle--large {
  width: 2.6em;
  height: 1.3em;

  .toggle-thumb {
    width: 1em;
    height: 1em;
  }
}

/* Checked State - Move thumb */
.toggle--checked .toggle-thumb {
  transform: translate(0.95em, -.02em);
}

.toggle--tiny.toggle--checked .toggle-thumb {
  transform: translate(.8em, -.05em);
}

.toggle--compact.toggle--checked .toggle-thumb {
  transform: translate(.95em, -.02em);
}

.toggle--large.toggle--checked .toggle-thumb {
  transform: translate(1.3em, -.02em);
}

/* Disabled State */
.toggle--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-wrapper:has(.toggle--disabled) {
  cursor: not-allowed;
}

/* Focus State */
.toggle-input:focus-visible+.toggle-track {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Label */
.toggle-label {
  color: var(--color-text);
  user-select: none;
}
</style>
